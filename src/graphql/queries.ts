import { gql } from "@apollo/client";


export const ARTISTS_QUERY = gql`
  query ArtistsConnection($where: ArtistWhere, $first: Int, $after: String) {
    artistsConnection(where: $where, first: $first, after: $after) {
      totalCount
      edges {
        cursor
        node {
          mbid
          name
          artistName
          gender
          bio
          formedDate
          disbandedDate
          profileImage
          links
        }
      }
      pageInfo {
        hasNextPage
        hasPreviousPage
        startCursor
        endCursor
      }
    }
  }
`;



export const ARTIST_PROFILE_QUERY = gql`
  query Works($artistId: ID!) {
    artistsConnection(where: { mbid: $artistId }) {
       edges {
            node {
                mbid
                name
                artistName
                bio
                formedDate
                disbandedDate
                profileImage
                genres
                links
                creditedOnSongConnection {
                    edges {
                        role
                        node {
                            mbid
                            title
                            coverImage
                            recordings {
                                mbid
                                title
                                duration
                                releaseDate
                                coverImage
                            }
                        }
                    }
                }
                albums {
                    mbid
                    title
                    releaseDate
                    coverArtData
                }
                creditedOnRecordingConnection {
                    totalCount
                    edges {
                        cursor
                        role
                        as
                        node {
                            mbid
                            title
                            duration
                            recordID
                            releaseDate
                            coverImage
                        }
                    }
                }
            }
        }
    }
  }
`;

export const PERFORMED_SONGS_QUERY = gql`
  query PerformedSongs($where: ArtistWhere, $first: Int, $after: String) {
    artistsConnection(where: $where) {
      edges {
        node {
          mbid
          performedConnection(first: $first, after: $after) {
            totalCount
            edges {
              node {
                mbid
                title
                duration
                recordID
                releaseDate
                coverImage
              }
              cursor
            }
            pageInfo {
              hasNextPage
              hasPreviousPage
              startCursor
              endCursor
            }
          }
        }
      }
    }
  }
`;

export const ALBUM_QUERY = gql`
  query AlbumsConnection($albumId: ID!) {
    albumsConnection(where: { mbid: $albumId }) {
      totalCount
      edges {
        node {
          mbid
          title
          releaseDate
          coverArtData
          creator {
                    mbid
                    name
                    artistName

                }
          tracks {
            mbid
            title
            duration
            recordID
            releaseDate
            coverImage
          }
        }
      }
    }
  }
`;

export const SONG_QUERY = gql`

query SongsConnection($songId: ID!) {
    songsConnection(where: { mbid: $songId }) {
        edges {
            cursor
            node {
                mbid
                title
                coverImage
                 tracks {
                        mbid
                        title
                        releaseDate
                        coverArtData
                        genres
                    }
                recordings {
                    mbid
                    title
                    duration
                    recordID
                    releaseDate
                    coverImage
                }
                creditedOnSongConnection {
                    edges {
                        role
                        as
                        node {
                            mbid
                            name
                            artistName
                            profileImage
                            links
                             albums {
                                mbid
                                title
                                releaseDate
                                coverArtData
                                genres
                            }
                        }
                    }
                }
            }
        }
    }
}

`;

export const RECORDING_QUERY = gql`
query RecordingsConnection($recordingId: ID!) {
    recordingsConnection(where: { mbid: $recordingId }) {
        edges {
            cursor
            node {
                mbid
                title
                duration
                recordID
                releaseDate
                coverImage
                song {
                    mbid
                    title
                    coverImage
                    recordings {
                        mbid
                        title
                        duration
                        recordID
                        releaseDate
                        coverImage
                        songConnection {
                            edges {
                                node {
                                    creditedOnSongConnection {
                                        edges {
                                            node {
                                                mbid
                                                name
                                                artistName
                                                profileImage
                                                links
                                            }
                                            role
                                            as
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                tracksConnection {
                    totalCount
                    edges {
                        node {
                            mbid
                            title
                            releaseDate
                            coverArtData
                            genres
                        }
                    }
                }
                creditedOnRecordingConnection {
                    edges {
                        role
                        as
                        node {
                            mbid
                            name
                            artistName
                            profileImage
                            links
                            albums {
                                mbid
                                title
                                coverArtData
                                genres
                            }
                        }
                    }
                }
                genres
                performed {
                    mbid
                    artistName
                }
            }
        }
    }
}

`;

export const SEARCH_QUERY = gql`
  query Search($searchString: String!, $entityType: String, $limit: Int) {
    search(searchString: $searchString, entityType: $entityType, limit: $limit) {
      id
      type
      displayName
      properties
      searchScore
    }
  }
`;

// New query for fetching all artists without pagination
export const ARTISTS_CONNECTION_ALL = gql`
  query ArtistsConnection {
    artistsConnection(where: { links_STARTS_WITH: "{" }) {
      totalCount
      edges {
        cursor
        node {
          mbid
          name
          artistName
          gender
          bio
          profileImage
          genres
        }
      }
    }
  }
`;
