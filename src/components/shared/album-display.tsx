"use client";

import Image from "next/image";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Disc, Calendar, Music } from "lucide-react";
import { useRouter } from "next/navigation";

// Common album interface that accommodates all data structures
export interface Album {
  mbid?: string;
  id?: string;
  title?: string | null;
  releaseDate?: string | null;
  coverArtData?: string | null;
  genres?: string[];
}

// Component props interface
export interface AlbumDisplayProps {
  albums: Album[];
  title?: string;
  variant?: "grid" | "list";
  showCard?: boolean;
  showGenres?: boolean;
  maxHeight?: string;
  gridCols?: string;
  onAlbumClick?: (albumId: string) => void;
  emptyMessage?: string;
  className?: string;
}

// Helper function to get cover art URL
export const getCoverArtUrl = (coverArtData?: string | null): string => {
  if (!coverArtData) return "/dummy-image.png?height=300&width=300";

  try {
    const parsed = JSON.parse(coverArtData);
    // Use 500px thumbnail for album covers, fallback to image_url
    return parsed?.thumbnails?.["500"] || parsed?.image_url || "/dummy-image.png?height=300&width=300";
  } catch (error) {
    console.warn("Failed to parse cover art data:", error);
    return "/dummy-image.png?height=300&width=300";
  }
};

export function AlbumDisplay({
  albums,
  title = "Albums",
  variant = "grid",
  showCard = true,
  showGenres = false,
  maxHeight,
  gridCols = "grid-cols-3 md:grid-cols-6",
  onAlbumClick,
  emptyMessage = "No albums available",
  className = "",
}: AlbumDisplayProps) {
  const router = useRouter();

  const handleAlbumClick = (albumId?: string) => {
    if (albumId) {
      if (onAlbumClick) {
        onAlbumClick(albumId);
      } else {
        router.push(`/album/?id=${albumId}`);
      }
    }
  };

  const getAlbumId = (album: Album) => album.mbid || album.id;

  if (!albums || albums.length === 0) {
    if (!showCard) return null;
    
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Disc className="w-5 h-5" />
            {title}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Music className="w-12 h-12 text-muted-foreground mx-auto mb-3" />
            <p className="text-muted-foreground">{emptyMessage}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const albumContent = variant === "grid" ? (
    <div className={`grid ${gridCols} gap-4`}>
      {albums.map((album) => {
        const albumId = getAlbumId(album);
        if (!albumId) return null;
        
        return (
          <div
            key={albumId}
            className="space-y-2 cursor-pointer group"
            onClick={() => handleAlbumClick(albumId)}
          >
            <div className="relative w-full aspect-square rounded-md overflow-hidden group-hover:shadow-lg transition-shadow">
              {/* Blurred background */}
              <div
                className="absolute inset-0 bg-cover bg-center blur-md scale-110"
                style={{
                  backgroundImage: `url(${getCoverArtUrl(album.coverArtData)})`
                }}
              />
              {/* Main image */}
              <Image
                src={getCoverArtUrl(album.coverArtData)}
                alt={`Album ${album.title || "Unknown"}`}
                width={150}
                height={150}
                className="object-contain w-full h-full relative z-10 group-hover:scale-105 transition-transform"
              />
            </div>
            <div className="space-y-1">
              <p className="font-medium text-sm group-hover:text-primary transition-colors overflow-hidden" style={{
                display: '-webkit-box',
                WebkitLineClamp: 2,
                WebkitBoxOrient: 'vertical'
              }}>
                {album.title || "Unknown Album"}
              </p>
              <p className="text-xs text-muted-foreground">
                {album.releaseDate
                  ? new Date(album.releaseDate).getFullYear()
                  : "Unknown Year"}
              </p>
              {showGenres && album.genres && album.genres.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {album.genres.slice(0, 2).map((genre: string, idx: number) => (
                    <span
                      key={idx}
                      className="text-xs bg-muted px-2 py-1 rounded-full"
                    >
                      {genre}
                    </span>
                  ))}
                  {album.genres.length > 2 && (
                    <span className="text-xs text-muted-foreground">
                      +{album.genres.length - 2}
                    </span>
                  )}
                </div>
              )}
            </div>
          </div>
        );
      })}
    </div>
  ) : (
    <div className={`space-y-4 ${maxHeight ? `max-h-[${maxHeight}] overflow-y-auto` : ""}`}>
      {albums.map((album, index) => {
        const albumId = getAlbumId(album);
        if (!albumId) return null;
        
        return (
          <div
            key={albumId || index}
            className="flex flex-col sm:flex-row gap-4 items-start p-3 rounded-lg cursor-pointer hover:bg-muted/50 transition-colors"
            onClick={() => handleAlbumClick(albumId)}
          >
            <Image
              src={getCoverArtUrl(album.coverArtData) ?? "/dummy-image.png"}
              alt={album.title ?? "Album Cover"}
              width={80}
              height={80}
              className="rounded shadow flex-shrink-0"
            />
            <div className="flex-1 min-w-0">
              <div className="text-lg font-semibold text-primary hover:underline truncate">
                {album.title ?? "Unknown Album"}
              </div>
              <div className="flex flex-wrap gap-3 mt-2 text-sm text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Calendar className="w-3 h-3" />
                  {album.releaseDate ?? "Unknown Date"}
                </div>
                {showGenres && album.genres && album.genres.length > 0 && (
                  <div className="flex items-center gap-1">
                    <Music className="w-3 h-3" />
                    {album.genres.slice(0, 2).join(", ")}
                    {album.genres.length > 2 && ` +${album.genres.length - 2} more`}
                  </div>
                )}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );

  if (!showCard) {
    return <div className={className}>{albumContent}</div>;
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Disc className="w-5 h-5" />
          {title}
          {albums.length > 0 && (
            <span className="ml-2 text-xs text-muted-foreground">
              ({albums.length})
            </span>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {albumContent}
      </CardContent>
    </Card>
  );
}
